import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Star,
  StarOff,
  Eye,
  EyeOff,
  Filter,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { toast } from 'sonner'

import { getAccommodations, deleteAccommodation, toggleAccommodationFeatured, updateAccommodationStatus } from '@/lib/accommodations'
import type {
  Accommodation,
  AccommodationFilters,
  AccommodationSort,
  AccommodationTableRow
} from '@/types/accommodation'
import {
  ACCOMMODATION_TYPE_LABELS,
  ACCOMMODATION_STATUS_CONFIG,
  DEFAULT_ACCOMMODATION_FILTERS,
  DEFAULT_ACCOMMODATION_SORT,
  DEFAULT_PAGINATION
} from '@/types/accommodation'

export default function AccommodationsAdmin() {
  const navigate = useNavigate()

  // State management
  const [accommodations, setAccommodations] = useState<Accommodation[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<AccommodationFilters>(DEFAULT_ACCOMMODATION_FILTERS)
  const [sort, setSort] = useState<AccommodationSort>(DEFAULT_ACCOMMODATION_SORT)
  const [pagination, setPagination] = useState(DEFAULT_PAGINATION)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [accommodationToDelete, setAccommodationToDelete] = useState<string | null>(null)

  // Load accommodations
  const loadAccommodations = async () => {
    try {
      setLoading(true)
      const response = await getAccommodations(filters, sort, {
        page: pagination.page,
        limit: pagination.limit
      })

      setAccommodations(response.data)
      setPagination(response.pagination)
    } catch (error) {
      console.error('Error loading accommodations:', error)
      toast.error('Failed to load accommodations')
    } finally {
      setLoading(false)
    }
  }

  // Load data on component mount and when filters/sort/pagination change
  useEffect(() => {
    loadAccommodations()
  }, [filters, sort, pagination.page])

  // Handle search
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // Handle filter changes
  const handleFilterChange = (key: keyof AccommodationFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  // Handle sort changes
  const handleSort = (field: AccommodationSort['field']) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const result = await deleteAccommodation(id)
      if (result.success) {
        toast.success('Accommodation deleted successfully')
        loadAccommodations()
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('Failed to delete accommodation')
    }
    setDeleteDialogOpen(false)
    setAccommodationToDelete(null)
  }

  // Handle featured toggle
  const handleToggleFeatured = async (id: string, featured: boolean) => {
    try {
      const result = await toggleAccommodationFeatured(id, !featured)
      if (result.success) {
        toast.success(`Accommodation ${!featured ? 'featured' : 'unfeatured'} successfully`)
        loadAccommodations()
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('Failed to update accommodation')
    }
  }

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: string) => {
    const newStatus = currentStatus === 'published' ? 'unpublished' : 'published'
    try {
      const result = await updateAccommodationStatus(id, newStatus as any)
      if (result.success) {
        toast.success(`Accommodation ${newStatus} successfully`)
        loadAccommodations()
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('Failed to update accommodation status')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Accommodations</h1>
          <p className="text-gray-600 mt-2">Manage rooms, suites, and tents</p>
        </div>
        <Button
          onClick={() => navigate('/admin/accommodations/new')}
          className="bg-amber-600 hover:bg-amber-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Accommodation
        </Button>
      </div>
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Accommodation Statistics</CardTitle>
          <CardDescription>Overview of accommodation usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">8</div>
              <div className="text-sm text-gray-500">Available</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">4</div>
              <div className="text-sm text-gray-500">Occupied</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">12</div>
              <div className="text-sm text-gray-500">Total</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
